import React from 'react'

interface WindowTopBarProps {
  className?: string
}

const WindowTopBar: React.FC<WindowTopBarProps> = ({ className = '' }) => {
  const handleMinimize = () => {
    if (window.electronAPI?.windowControls) {
      window.electronAPI.windowControls.minimize()
    }
  }

  const handleMaximize = () => {
    if (window.electronAPI?.windowControls) {
      window.electronAPI.windowControls.maximize()
    }
  }

  const handleClose = () => {
    if (window.electronAPI?.windowControls) {
      window.electronAPI.windowControls.close()
    }
  }

  return (
    <div className={`h-6 glass-subtle flex items-center justify-end px-2 ${className}`}>
      <div className="flex items-center gap-1">
        <button 
          onClick={handleMaximize}
          className="w-4 h-4 flex items-center justify-center hover:bg-white/10 rounded transition-colors"
          title="Maximize/Restore"
        >
          <i className="fa-solid fa-expand text-gray-300 text-xs"></i>
        </button>
        <button 
          onClick={handleClose}
          className="w-4 h-4 flex items-center justify-center hover:bg-red-500/20 rounded transition-colors"
          title="Close"
        >
          <i className="fa-solid fa-xmark text-gray-300 hover:text-white text-xs"></i>
        </button>
      </div>
    </div>
  )
}

export default WindowTopBar
